{"version": 3, "file": "variants.js", "sourceRoot": "", "sources": ["../ts/variants.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,6CAAiE;AA+CjE;;;;;;;GAOG;AACI,KAAK,UAAU,oBAAoB;AACxC;;GAEG;AACH,UAA4B,oBAAY;IAExC,MAAM,CAAC,gBAAgB,EAAE,UAAU,EAAE,EAAE,iBAAiB,EAAE,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAC9E,OAAO,CAAC,kBAAkB,EAAE;QAC5B,OAAO,CAAC,SAAS,EAAE;QACnB,kDAAO,aAAa,IAAE,IAAI,CAAC,6BAAgB,CAAC;KAC7C,CAAC,CAAA;IACF,MAAM,UAAU,GAAG,MAAM,gBAAgB,EAAE,CAAA;IAC3C,UAAU,CAAC,IAAI,GAAG,MAAM,CAAA;IACxB,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAA;IACtC,OAAO,IAAI,iBAAiB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;AAC/C,CAAC;AAfD,oDAeC;AAED;;;;;;;;;;;;GAYG;AACI,KAAK,UAAU,yBAAyB;AAC7C;;GAEG;AACH,UAA6B,qBAAa;IAE1C,MAAM,CAAC,gBAAgB,EAAE,eAAe,EAAE,EAAE,sBAAsB,EAAE,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACxF,OAAO,CAAC,kBAAkB,EAAE;QAC5B,OAAO,CAAC,SAAS,EAAE;QACnB,kDAAO,sBAAsB,IAAE,IAAI,CAAC,6BAAgB,CAAC;KACtD,CAAC,CAAA;IACF,MAAM,UAAU,GAAG,MAAM,gBAAgB,EAAE,CAAA;IAC3C,UAAU,CAAC,IAAI,GAAG,OAAO,CAAA;IACzB,MAAM,GAAG,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;IAC3C,OAAO,IAAI,sBAAsB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;AACpD,CAAC;AAfD,8DAeC;AAED;;;;;GAKG;AACH,SAAgB,qBAAqB,CAAI,EAAoB;IAC3D,IAAI,OAA+B,CAAA;IACnC,OAAO,GAAG,EAAE;QACV,OAAO,CAAC,OAAO,KAAP,OAAO,GAAK,EAAE,EAAE,EAAC,CAAA;IAC3B,CAAC,CAAA;AACH,CAAC;AALD,sDAKC;AAED;;;;;;;;;;;GAWG;AACU,QAAA,UAAU,GAAqB;IAC1C,IAAI,EAAE,MAAM;IACZ,KAAK,CAAC,SAAS;QACb,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAClC,iEAAiE;QACjE,0CAA0C;IAC5C,CAAC;IACD,KAAK,CAAC,kBAAkB;QACtB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAClC,+EAA+E;QAC/E,uCAAuC;IACzC,CAAC;CACF,CAAA;AAED;;;GAGG;AACU,QAAA,YAAY,GAAqB;IAC5C,IAAI,EAAE,MAAM;IACZ,KAAK,CAAC,SAAS;QACb,MAAM,GAAG,GAAG,wDAAa,sCAAsC,GAAC,CAAA;QAChE,OAAO,IAAA,6BAAgB,EAAC,GAAG,CAAC,CAAC,UAAU,CAAA;IACzC,CAAC;IACD,KAAK,CAAC,kBAAkB;QACtB,MAAM,GAAG,GAAG,wDAAa,oDAAoD,GAAC,CAAA;QAC9E,OAAO,IAAA,6BAAgB,EAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;CACF,CAAA;AAED;;;;;GAKG;AACU,QAAA,WAAW,GAAsB;IAC5C,IAAI,EAAE,OAAO;IACb,KAAK,CAAC,SAAS;QACb,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAClC,qEAAqE;QACrE,+CAA+C;IACjD,CAAC;IACD,KAAK,CAAC,kBAAkB;QACtB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAClC,mFAAmF;QACnF,uCAAuC;IACzC,CAAC;CACF,CAAA;AAED;;GAEG;AACU,QAAA,aAAa,GAAsB;IAC9C,IAAI,EAAE,OAAO;IACb,KAAK,CAAC,SAAS;QACb,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAClC,uEAAuE;QACvE,+CAA+C;IACjD,CAAC;IACD,KAAK,CAAC,kBAAkB;QACtB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAClC,qFAAqF;QACrF,uCAAuC;IACzC,CAAC;CACF,CAAA", "sourcesContent": ["//import type { <PERSON>J<PERSON>sync<PERSON><PERSON> as Debug<PERSON>yncify<PERSON><PERSON> } from \"./generated/ffi.WASM_DEBUG_ASYNCIFY\"\n//import type { QuickJSAsyncFFI as ReleaseAsyncifyFFI } from \"./generated/ffi.WASM_RELEASE_ASYNCIFY\"\n//import type { <PERSON>J<PERSON><PERSON> as DebugSync<PERSON>I } from \"./generated/ffi.WASM_DEBUG_SYNC\"\nimport type { QuickJSFFI as ReleaseSyncFFI } from \"./generated/ffi.WASM_RELEASE_SYNC\"\nimport type {\n  EmscriptenModuleLoader,\n  QuickJSEmscriptenModule,\n  QuickJSAsyncEmscriptenModule,\n} from \"./emscripten-types\"\nimport type { QuickJSWASMModule } from \"./module\"\nimport type { QuickJSAsyncWASMModule } from \"./module-asyncify\"\nimport { unwrapTypescript, unwrapJavascript } from \"./esmHelpers\"\n\n/** @private */\nexport type QuickJSFFI = ReleaseSyncFFI\n/** @private */\nexport type QuickJSFFIConstructor = typeof ReleaseSyncFFI\n/** @private */\nexport type QuickJSAsyncFFI = any\n/** @private */\nexport type QuickJSAsyncFFIConstructor = any\n\n/**\n * quickjs-emscripten provides multiple build variants of the core WebAssembly\n * module. These variants are each intended for a different use case.\n *\n * To create an instance of the library using a specific build variant, pass the\n * build variant to {@link newQuickJSWASMModule} or {@link newQuickJSAsyncWASMModule}.\n *\n * Synchronous build variants:\n *\n * - {@link RELEASE_SYNC} - This is the default synchronous variant, for general purpose use.\n * - {@link DEBUG_SYNC} - Synchronous build variant for debugging memory leaks.\n */\nexport interface SyncBuildVariant {\n  type: \"sync\"\n  importFFI: () => Promise<QuickJSFFIConstructor>\n  importModuleLoader: () => Promise<EmscriptenModuleLoader<QuickJSEmscriptenModule>>\n}\n\n/**\n * quickjs-emscripten provides multiple build variants of the core WebAssembly\n * module. These variants are each intended for a different use case.\n *\n * To create an instance of the library using a specific build variant, pass the\n * build variant to {@link newQuickJSWASMModule} or {@link newQuickJSAsyncWASMModule}.\n *\n * Asyncified build variants:\n *\n * - {@link RELEASE_ASYNC} - This is the default asyncified build variant, for general purpose use.\n * - {@link DEBUG_ASYNC} - Asyncified build variant with debug logging.\n */\nexport interface AsyncBuildVariant {\n  type: \"async\"\n  importFFI: () => Promise<QuickJSAsyncFFIConstructor>\n  importModuleLoader: () => Promise<EmscriptenModuleLoader<QuickJSAsyncEmscriptenModule>>\n}\n\n/**\n * Create a new, completely isolated WebAssembly module containing the QuickJS library.\n * See the documentation on [[QuickJSWASMModule]].\n *\n * Note that there is a hard limit on the number of WebAssembly modules in older\n * versions of v8:\n * https://bugs.chromium.org/p/v8/issues/detail?id=12076\n */\nexport async function newQuickJSWASMModule(\n  /**\n   * Optionally, pass a {@link SyncBuildVariant} to construct a different WebAssembly module.\n   */\n  variant: SyncBuildVariant = RELEASE_SYNC\n): Promise<QuickJSWASMModule> {\n  const [wasmModuleLoader, QuickJSFFI, { QuickJSWASMModule }] = await Promise.all([\n    variant.importModuleLoader(),\n    variant.importFFI(),\n    import(\"./module.js\").then(unwrapTypescript),\n  ])\n  const wasmModule = await wasmModuleLoader()\n  wasmModule.type = \"sync\"\n  const ffi = new QuickJSFFI(wasmModule)\n  return new QuickJSWASMModule(wasmModule, ffi)\n}\n\n/**\n * Create a new, completely isolated WebAssembly module containing a version of the QuickJS library\n * compiled with Emscripten's [ASYNCIFY](https://emscripten.org/docs/porting/asyncify.html) transform.\n *\n * This version of the library offers features that enable synchronous code\n * inside the VM to interact with asynchronous code in the host environment.\n * See the documentation on [[QuickJSAsyncWASMModule]], [[QuickJSAsyncRuntime]],\n * and [[QuickJSAsyncContext]].\n *\n * Note that there is a hard limit on the number of WebAssembly modules in older\n * versions of v8:\n * https://bugs.chromium.org/p/v8/issues/detail?id=12076\n */\nexport async function newQuickJSAsyncWASMModule(\n  /**\n   * Optionally, pass a {@link AsyncBuildVariant} to construct a different WebAssembly module.\n   */\n  variant: AsyncBuildVariant = RELEASE_ASYNC\n): Promise<QuickJSAsyncWASMModule> {\n  const [wasmModuleLoader, QuickJSAsyncFFI, { QuickJSAsyncWASMModule }] = await Promise.all([\n    variant.importModuleLoader(),\n    variant.importFFI(),\n    import(\"./module-asyncify.js\").then(unwrapTypescript),\n  ])\n  const wasmModule = await wasmModuleLoader()\n  wasmModule.type = \"async\"\n  const ffi = new QuickJSAsyncFFI(wasmModule)\n  return new QuickJSAsyncWASMModule(wasmModule, ffi)\n}\n\n/**\n * Helper intended to memoize the creation of a WebAssembly module.\n * ```typescript\n * const getDebugModule = memoizePromiseFactory(() => newQuickJSWASMModule(DEBUG_SYNC))\n * ```\n */\nexport function memoizePromiseFactory<T>(fn: () => Promise<T>): () => Promise<T> {\n  let promise: Promise<T> | undefined\n  return () => {\n    return (promise ??= fn())\n  }\n}\n\n/**\n * This build variant is compiled with `-fsanitize=leak`. It instruments all\n * memory allocations and when combined with sourcemaps, can present stack trace\n * locations where memory leaks occur.\n *\n * See [[TestQuickJSWASMModule]] which provides access to the leak sanitizer via\n * {@link TestQuickJSWASMModule.assertNoMemoryAllocated}.\n *\n * The downside is that it's 100-1000x slower than the other variants.\n * Suggested use case: automated testing, regression testing, and interactive\n * debugging.\n */\nexport const DEBUG_SYNC: SyncBuildVariant = {\n  type: \"sync\",\n  async importFFI() {\n    throw new Error(\"not implemented\")\n    // const mod = await import(\"./generated/ffi.WASM_DEBUG_SYNC.js\")\n    // return unwrapTypescript(mod).QuickJSFFI\n  },\n  async importModuleLoader() {\n    throw new Error(\"not implemented\")\n    // const mod = await import(\"./generated/emscripten-module.WASM_DEBUG_SYNC.js\")\n    // return unwrapJavascript(mod).default\n  },\n}\n\n/**\n * This is the default (synchronous) build variant.\n * {@link getQuickJS} returns a memoized instance of this build variant.\n */\nexport const RELEASE_SYNC: SyncBuildVariant = {\n  type: \"sync\",\n  async importFFI() {\n    const mod = await import(\"./generated/ffi.WASM_RELEASE_SYNC.js\")\n    return unwrapTypescript(mod).QuickJSFFI\n  },\n  async importModuleLoader() {\n    const mod = await import(\"./generated/emscripten-module.WASM_RELEASE_SYNC.js\")\n    return unwrapJavascript(mod)\n  },\n}\n\n/**\n * The async debug build variant may or may not have the sanitizer enabled.\n * It does print a lot of debug logs.\n *\n * Suggested use case: interactive debugging only.\n */\nexport const DEBUG_ASYNC: AsyncBuildVariant = {\n  type: \"async\",\n  async importFFI() {\n    throw new Error(\"not implemented\")\n    // const mod = await import(\"./generated/ffi.WASM_DEBUG_ASYNCIFY.js\")\n    // return unwrapTypescript(mod).QuickJSAsyncFFI\n  },\n  async importModuleLoader() {\n    throw new Error(\"not implemented\")\n    // const mod = await import(\"./generated/emscripten-module.WASM_DEBUG_ASYNCIFY.js\")\n    // return unwrapJavascript(mod).default\n  },\n}\n\n/**\n * This is the default asyncified build variant.\n */\nexport const RELEASE_ASYNC: AsyncBuildVariant = {\n  type: \"async\",\n  async importFFI() {\n    throw new Error(\"not implemented\")\n    // const mod = await import(\"./generated/ffi.WASM_RELEASE_ASYNCIFY.js\")\n    // return unwrapTypescript(mod).QuickJSAsyncFFI\n  },\n  async importModuleLoader() {\n    throw new Error(\"not implemented\")\n    // const mod = await import(\"./generated/emscripten-module.WASM_RELEASE_ASYNCIFY.js\")\n    // return unwrapJavascript(mod).default\n  },\n}\n"]}