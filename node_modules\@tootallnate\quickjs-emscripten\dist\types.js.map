{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../ts/types.ts"], "names": [], "mappings": ";;;AAMA,2CAA8F;AA+F9F,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AAwDzC,6BAA6B;AAC7B,MAAM,qBAAqB,GAAG;IAC5B,aAAa;IACb,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,aAAa;IACb,SAAS;CACD,CAAA;AAEV;;GAEG;AACU,QAAA,iBAAiB,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAA;AAwC5D,yDAAyD;AACzD,SAAgB,kBAAkB,CAAC,WAAoD;IACrF,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnC,OAAO,WAAW,CAAA;KACnB;IAED,IAAI,WAAW,KAAK,SAAS,EAAE;QAC7B,OAAO,CAAC,CAAA;KACT;IAED,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG,WAAW,CAAA;IAC1E,IAAI,KAAK,GAAG,CAAC,CAAA;IACb,IAAI,IAAI,KAAK,QAAQ;QAAE,KAAK,IAAI,qBAAS,CAAC,mBAAmB,CAAA;IAC7D,IAAI,IAAI,KAAK,QAAQ;QAAE,KAAK,IAAI,qBAAS,CAAC,mBAAmB,CAAA;IAC7D,IAAI,MAAM;QAAE,KAAK,IAAI,qBAAS,CAAC,mBAAmB,CAAA;IAClD,IAAI,KAAK;QAAE,KAAK,IAAI,qBAAS,CAAC,kBAAkB,CAAA;IAChD,IAAI,WAAW;QAAE,KAAK,IAAI,qBAAS,CAAC,yBAAyB,CAAA;IAC7D,IAAI,gBAAgB;QAAE,KAAK,IAAI,qBAAS,CAAC,8BAA8B,CAAA;IACvE,OAAO,KAAK,CAAA;AACd,CAAC;AAlBD,gDAkBC;AAOD,SAAgB,MAAM,CAAI,GAAG,MAAkC;IAC7D,IAAI,MAAM,GAAQ,EAAE,CAAA;IACpB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;SAC9B;KACF;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AARD,wBAQC", "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, QuickJSAsyncFFI } from \"./variants\"\nimport type { QuickJSContext } from \"./context\"\nimport type { <PERSON>OrFail, VmFunctionImplementation } from \"./vm-interface\"\nimport type { Disposable, Lifetime } from \"./lifetime\"\nimport type { <PERSON>JSAsyncContext } from \"./context-asyncify\"\nimport type { <PERSON><PERSON>t<PERSON><PERSON><PERSON>, QuickJSRuntime } from \"./runtime\"\nimport { <PERSON>l<PERSON><PERSON>s, JSContextPointer, JSValueConstPointer, JSValuePointer } from \"./types-ffi\"\n\nexport type EitherFFI = QuickJSFFI | QuickJSAsyncFFI\n\n/**\n * A QuickJSHandle to a constant that will never change, and does not need to\n * be disposed.\n */\nexport type StaticJSValue = Lifetime<JSValueConstPointer, JSValueConstPointer, QuickJSRuntime>\n\n/**\n * A QuickJSHandle to a borrowed value that does not need to be disposed.\n *\n * In QuickJS, a JSValueConst is a \"borrowed\" reference that isn't owned by the\n * current scope. That means that the current scope should not `JS_FreeValue`\n * it, or retain a reference to it after the scope exits, because it may be\n * freed by its owner.\n *\n * quickjs-emscripten takes care of disposing JSValueConst references.\n */\nexport type JSValueConst = Lifetime<JSValueConstPointer, JSValuePointer, QuickJSRuntime>\n\n/**\n * A owned QuickJSHandle that should be disposed or returned.\n *\n * The QuickJS interpreter passes Javascript values between functions as\n * `JSValue` structs that references some internal data. Because passing\n * structs cross the Empscripten FFI interfaces is bothersome, we use pointers\n * to these structs instead.\n *\n * A JSValue reference is \"owned\" in its scope. before exiting the scope, it\n * should be freed,  by calling `JS_FreeValue(ctx, js_value)`) or returned from\n * the scope. We extend that contract - a JSValuePointer (`JSValue*`) must also\n * be `free`d.\n *\n * You can do so from Javascript by calling the .dispose() method.\n */\nexport type JSValue = Lifetime<JSValuePointer, JSValuePointer, QuickJSRuntime>\n\n/**\n * Wraps a C pointer to a QuickJS JSValue, which represents a Javascript value inside\n * a QuickJS virtual machine.\n *\n * Values must not be shared between QuickJSContext instances.\n * You must dispose of any handles you create by calling the `.dispose()` method.\n */\nexport type QuickJSHandle = StaticJSValue | JSValue | JSValueConst\n\nexport type JSModuleExport =\n  | {\n      type: \"function\"\n      name: string\n      implementation: (vm: QuickJSContext) => VmFunctionImplementation<QuickJSHandle>\n    }\n  | { type: \"value\"; name: string; value: (vm: QuickJSContext) => QuickJSHandle }\n\nexport interface JSModuleDefinition {\n  name: string\n  exports: JSModuleExport[]\n}\n\nexport type JSModuleLoadSuccess = string\nexport type JSModuleLoadFailure = Error | QuickJSHandle\nexport type JSModuleLoadResult =\n  | JSModuleLoadSuccess\n  | SuccessOrFail<JSModuleLoadSuccess, JSModuleLoadFailure>\n\nexport interface JSModuleLoaderAsync {\n  /** Load module (async) */\n  (moduleName: string, context: QuickJSAsyncContext):\n    | JSModuleLoadResult\n    | Promise<JSModuleLoadResult>\n}\nexport interface JSModuleLoader {\n  /** Load module (sync) */\n  (moduleName: string, context: QuickJSContext): JSModuleLoadResult\n}\n\nexport type JSModuleNormalizeSuccess = string\nexport type JSModuleNormalizeFailure = Error | QuickJSHandle\nexport type JSModuleNormalizeResult =\n  | JSModuleNormalizeSuccess\n  | SuccessOrFail<JSModuleNormalizeSuccess, JSModuleNormalizeFailure>\n\nexport interface JSModuleNormalizerAsync {\n  (baseModuleName: string, requestedName: string, vm: QuickJSAsyncContext):\n    | JSModuleNormalizeResult\n    | Promise<JSModuleNormalizeResult>\n}\nexport interface JSModuleNormalizer extends JSModuleNormalizerAsync {\n  (baseModuleName: string, requestedName: string, vm: QuickJSContext): JSModuleNormalizeResult\n}\n\ntype TODO<hint extends string = \"?\", typeHint = unknown> = never\n\nconst UnstableSymbol = Symbol(\"Unstable\")\n\nexport type PartiallyImplemented<T> = never &\n  T & {\n    [UnstableSymbol]: \"This feature may unimplemented, broken, throw errors, etc.\"\n  }\n\nexport interface RuntimeOptionsBase {\n  interruptHandler?: InterruptHandler\n  maxStackSizeBytes?: number\n  memoryLimitBytes?: number\n\n  promiseRejectionHandler?: TODO<\"JSHostPromiseRejectionTracker\">\n  runtimeInfo?: TODO<\"JS_SetRuntimeInfo\", string>\n  gcThreshold?: TODO<\"JS_SetGCThreshold\", number>\n  sharedArrayBufferFunctions?: TODO<\n    \"JS_SetJSSharedArrayBufferFunctions\",\n    { sab_alloc: TODO; sab_free: TODO; sab_dup: TODO; sab_opaque: TODO }\n  >\n\n  /**\n   * Extra lifetimes the runtime should dispose of after it is destroyed.\n   * @private\n   */\n  ownedLifetimes?: Disposable[]\n}\n\nexport interface RuntimeOptions extends RuntimeOptionsBase {\n  moduleLoader?: JSModuleLoader\n}\n\nexport interface AsyncRuntimeOptions extends RuntimeOptionsBase {\n  moduleLoader?: JSModuleLoaderAsync | JSModuleLoader\n}\n\n/**\n * Work in progress.\n */\nexport type Intrinsic =\n  | \"BaseObjects\"\n  | \"Date\"\n  | \"Eval\"\n  | \"StringNormalize\"\n  | \"RegExp\"\n  | \"RegExpCompiler\"\n  | \"JSON\"\n  | \"Proxy\"\n  | \"MapSet\"\n  | \"TypedArrays\"\n  | \"Promise\"\n  | \"BigInt\"\n  | \"BigFloat\"\n  | \"BigDecimal\"\n  | \"OperatorOverloading\"\n  | \"BignumExt\"\n\n// For informational purposes\nconst DefaultIntrinsicsList = [\n  \"BaseObjects\",\n  \"Date\",\n  \"Eval\",\n  \"StringNormalize\",\n  \"RegExp\",\n  \"JSON\",\n  \"Proxy\",\n  \"MapSet\",\n  \"TypedArrays\",\n  \"Promise\",\n] as const\n\n/**\n * Work in progress.\n */\nexport const DefaultIntrinsics = Symbol(\"DefaultIntrinsics\")\n\nexport interface ContextOptions {\n  /**\n   * What built-in objects and language features to enable?\n   * If unset, the default intrinsics will be used.\n   * To omit all intrinsics, pass an empty array.\n   */\n  intrinsics?: PartiallyImplemented<Intrinsic[]> | typeof DefaultIntrinsics\n\n  /**\n   * Wrap the provided context instead of constructing a new one.\n   * @private\n   */\n  contextPointer?: JSContextPointer\n\n  /**\n   * Extra lifetimes the context should dispose of after it is destroyed.\n   * @private\n   */\n  ownedLifetimes?: Disposable[]\n}\n\nexport interface ContextEvalOptions {\n  /** Global code (default) */\n  type?: \"global\" | \"module\"\n  /** Force \"strict\" mode */\n  strict?: boolean\n  /** Force \"strip\" mode */\n  strip?: boolean\n  /**\n   * compile but do not run. The result is an object with a\n   * JS_TAG_FUNCTION_BYTECODE or JS_TAG_MODULE tag. It can be executed\n   * with JS_EvalFunction().\n   */\n  compileOnly?: boolean\n  /** don't include the stack frames before this eval in the Error() backtraces */\n  backtraceBarrier?: boolean\n}\n\n/** Convert [[ContextEvalOptions]] to a bitfield flags */\nexport function evalOptionsToFlags(evalOptions: ContextEvalOptions | number | undefined): number {\n  if (typeof evalOptions === \"number\") {\n    return evalOptions\n  }\n\n  if (evalOptions === undefined) {\n    return 0\n  }\n\n  const { type, strict, strip, compileOnly, backtraceBarrier } = evalOptions\n  let flags = 0\n  if (type === \"global\") flags |= EvalFlags.JS_EVAL_TYPE_GLOBAL\n  if (type === \"module\") flags |= EvalFlags.JS_EVAL_TYPE_MODULE\n  if (strict) flags |= EvalFlags.JS_EVAL_FLAG_STRICT\n  if (strip) flags |= EvalFlags.JS_EVAL_FLAG_STRIP\n  if (compileOnly) flags |= EvalFlags.JS_EVAL_FLAG_COMPILE_ONLY\n  if (backtraceBarrier) flags |= EvalFlags.JS_EVAL_FLAG_BACKTRACE_BARRIER\n  return flags\n}\n\nexport type PromiseExecutor<ResolveT, RejectT> = (\n  resolve: (value: ResolveT | PromiseLike<ResolveT>) => void,\n  reject: (reason: RejectT) => void\n) => void\n\nexport function concat<T>(...values: Array<T[] | T | undefined>): T[] {\n  let result: T[] = []\n  for (const value of values) {\n    if (value !== undefined) {\n      result = result.concat(value)\n    }\n  }\n  return result\n}\n"]}