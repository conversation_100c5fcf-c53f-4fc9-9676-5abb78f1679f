/**
 * Irys 每日领水签到脚本
 * 实现流程：
 * 1. 访问 OKX 钱包页面复制地址
 * 2. 访问 Galxe 任务页面
 * 3. 检查登录状态并执行相应流程
 * 4. 执行领水签到流程
 * 5. 完成任务，关闭页面
 * 
 * 包含反机器人检测措施：
 * - 随机延迟等待
 * - 鼠标移动模拟人手抖动
 * - 每步骤重试1-2次机制
 */

async function irysDailyWater(browser, config) {
  const okxUrl = 'https://web3.okx.com/zh-hans/portfolio';
  const galxeUrl = 'https://app.galxe.com/quest/Irys/GCxxCtmLMP';
  let copiedAddress = '';
  
  try {
    console.log('🔄 开始执行 Irys 每日领水签到脚本...');
    
    // ===== 步骤1: 复制地址流程 =====
    const page1 = await browser.newPage();
    
    await withRetry(async () => {
      console.log('正在访问 OKX 钱包页面...');
      await page1.goto(okxUrl, { waitUntil: 'networkidle2', timeout: 60000 });
      
      // 等待7-10秒以供页面加载完成
      const initialWaitTime = Math.floor(Math.random() * (10000 - 7000 + 1)) + 7000;
      console.log(`等待 ${initialWaitTime/1000} 秒以确保页面加载完成...`);
      await new Promise(resolve => setTimeout(resolve, initialWaitTime));
      
      console.log('检查页面状态...');
      
      // 检查是否需要连接钱包
      const connectButton = await page1.$('div.wallet-pc-connect-button');
      let connectButtonWithText = null;
      
      if (!connectButton) {
        // 查找包含"连接钱包"文字的元素
        connectButtonWithText = await page1.evaluateHandle(() => {
          const elements = Array.from(document.querySelectorAll('*'));
          return elements.find(el => el.textContent && el.textContent.includes('连接钱包'));
        });
      }
      
      if (connectButton || (connectButtonWithText && await connectButtonWithText.asElement())) {
        console.log('🔑 检测到需要连接钱包，开始登录流程...');
        
        // 点击连接钱包按钮
        if (connectButton) {
          await humanLikeMouseMove(page1, 'div.wallet-pc-connect-button');
          await page1.click('div.wallet-pc-connect-button');
        } else {
          await connectButtonWithText.click();
        }
        
        // 等待3-5秒
        const loginWaitTime = Math.floor(Math.random() * (5000 - 3000 + 1)) + 3000;
        console.log(`等待 ${loginWaitTime/1000} 秒...`);
        await new Promise(resolve => setTimeout(resolve, loginWaitTime));
        
        // 点击 button.wallet-btn
        await withRetry(async () => {
          await page1.waitForSelector('button.wallet-btn', { timeout: 10000 });
          await humanLikeMouseMove(page1, 'button.wallet-btn');
          await page1.click('button.wallet-btn');
          console.log('✅ 已点击钱包按钮');
        }, 2, '点击钱包按钮');
        
        // 等待3-5秒，登录流程结束
        const finalWaitTime = Math.floor(Math.random() * (5000 - 3000 + 1)) + 3000;
        console.log(`等待 ${finalWaitTime/1000} 秒以确保登录完成...`);
        await new Promise(resolve => setTimeout(resolve, finalWaitTime));
      }
      
      console.log('✅ 成功访问 OKX 页面');
    }, 2, '访问 OKX 页面');
    
    // 复制地址流程
    await withRetry(async () => {
      console.log('开始复制地址流程...');
      
      // 点击复制图标
      await page1.waitForSelector('i.okx-wallet-copy', { timeout: 10000 });
      await humanLikeMouseMove(page1, 'i.okx-wallet-copy');
      await page1.click('i.okx-wallet-copy');
      console.log('✅ 已点击复制图标');
      
      // 等待1-2秒
      await randomDelay(1000, 2000);
      
      // 点击输入框并输入 Ethereum
      await page1.waitForSelector('input#\\:r7m\\:', { timeout: 10000 });
      await humanLikeMouseMove(page1, 'input#\\:r7m\\:');
      await page1.click('input#\\:r7m\\:');
      await page1.type('input#\\:r7m\\:', 'Ethereum');
      console.log('✅ 已输入 Ethereum');
      
      // 等待1-2秒
      await randomDelay(1000, 2000);
      
      // 点击复制地址按钮
      await page1.waitForSelector('#\\:rb\\:-1 .icon', { timeout: 10000 });
      await humanLikeMouseMove(page1, '#\\:rb\\:-1 .icon');
      await page1.click('#\\:rb\\:-1 .icon');
      console.log('✅ 已复制地址');
      
      // 获取剪贴板内容
      copiedAddress = await page1.evaluate(async () => {
        try {
          return await navigator.clipboard.readText();
        } catch (e) {
          return '';
        }
      });
      
      if (copiedAddress) {
        console.log(`✅ 成功复制地址: ${copiedAddress.substring(0, 10)}...`);
      } else {
        console.log('⚠️ 无法获取复制的地址，将继续执行');
      }
      
    }, 2, '复制地址流程');
    
    // 关闭第一个页面
    await page1.close();
    console.log('✅ 复制地址流程完成，已关闭 OKX 页面');
    
    // ===== 步骤2: 访问 Galxe 页面 =====
    const page2 = await browser.newPage();
    
    await withRetry(async () => {
      console.log('正在访问 Galxe 任务页面...');
      await page2.goto(galxeUrl, { waitUntil: 'networkidle2', timeout: 60000 });
      
      // 等待10-13秒以供页面加载完成
      const galxeWaitTime = Math.floor(Math.random() * (13000 - 10000 + 1)) + 10000;
      console.log(`等待 ${galxeWaitTime/1000} 秒以确保页面加载完成...`);
      await new Promise(resolve => setTimeout(resolve, galxeWaitTime));
      
      console.log('✅ 成功访问 Galxe 页面');
    }, 2, '访问 Galxe 页面');
    
    // ===== 步骤3: 检查登录状态 =====
    const pageStatus = await withRetry(async () => {
      console.log('检查页面登录状态...');
      
      // 检查登录按钮
      const loginButton = await page2.$('button.px-\\[24px\\]');
      let loginButtonWithText = null;
      
      if (!loginButton) {
        // 查找包含"log in"文字的元素
        loginButtonWithText = await page2.evaluateHandle(() => {
          const elements = Array.from(document.querySelectorAll('*'));
          return elements.find(el => el.textContent && el.textContent.toLowerCase().includes('log in'));
        });
      }
      
      if (loginButton || (loginButtonWithText && await loginButtonWithText.asElement())) {
        console.log('🔑 检测到需要登录');
        return { type: 'login' };
      }
      
      // 检查跳过教程按钮
      const skipButton = await page2.$('button.w-\\[88px\\]');
      if (skipButton) {
        console.log('📚 检测到教程页面');
        return { type: 'tutorial' };
      }
      
      console.log('✅ 已登录，直接进入签到流程');
      return { type: 'checkin' };
      
    }, 2, '检查页面状态');
    
    // ===== 步骤4: 根据状态执行相应流程 =====
    if (pageStatus.type === 'login') {
      await executeLoginFlow(page2);
    } else if (pageStatus.type === 'tutorial') {
      await executeTutorialFlow(page2);
    }
    
    // ===== 步骤5: 执行签到领水流程 =====
    await executeCheckinFlow(page2, copiedAddress);
    
    // ===== 步骤6: 完成任务 =====
    console.log('✅ Irys 每日领水签到任务已完成');
    await randomDelay(3000, 5000);
    
    // 关闭页面
    await page2.close();
    console.log('✅ Irys 脚本执行成功，页面已关闭');
    
  } catch (error) {
    console.error(`❌ Irys 脚本执行失败: ${error.message}`);
    // 确保页面关闭，避免资源泄漏
    const pages = await browser.pages();
    for (const page of pages) {
      if (!page.isClosed()) {
        await page.close().catch(() => {});
      }
    }
  }
}

/**
 * 执行登录流程
 * @param {Page} page Puppeteer页面对象
 */
async function executeLoginFlow(page) {
  await withRetry(async () => {
    console.log('开始登录流程...');

    // 点击登录按钮
    const loginButton = await page.$('button.px-\\[24px\\]');
    let loginButtonWithText = null;

    if (!loginButton) {
      loginButtonWithText = await page.evaluateHandle(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        return elements.find(el => el.textContent && el.textContent.toLowerCase().includes('log in'));
      });
    }

    if (loginButton) {
      await humanLikeMouseMove(page, 'button.px-\\[24px\\]');
      await page.click('button.px-\\[24px\\]');
    } else if (loginButtonWithText && await loginButtonWithText.asElement()) {
      await loginButtonWithText.click();
    }

    console.log('✅ 已点击登录按钮');

    // 等待3-5秒
    const waitTime = Math.floor(Math.random() * (5000 - 3000 + 1)) + 3000;
    console.log(`等待 ${waitTime/1000} 秒...`);
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // 检查登录方式
    const recentDiv = await page.$('div.rounded-6:nth-child(2)');
    if (recentDiv) {
      const divText = await page.evaluate(el => el.textContent, recentDiv);

      if (divText.includes('Recent')) {
        console.log('🔄 检测到Recent登录方式，执行登录流程方式一');
        await humanLikeMouseMove(page, 'div.mb-5:nth-child(1)');
        await page.click('div.mb-5:nth-child(1)');

        // 等待10-15秒以供其他程序进行钱包登录操作
        const walletWaitTime = Math.floor(Math.random() * (15000 - 10000 + 1)) + 10000;
        console.log(`等待 ${walletWaitTime/1000} 秒以供钱包登录...`);
        await new Promise(resolve => setTimeout(resolve, walletWaitTime));

      } else if (divText.includes('Solana')) {
        console.log('🔄 检测到Solana登录方式，执行登录流程方式二');

        // 优先查找包含OKX文字的div元素
        const okxDiv = await page.evaluateHandle(() => {
          const elements = Array.from(document.querySelectorAll('div'));
          return elements.find(el => el.textContent && el.textContent.includes('OKX'));
        });

        if (okxDiv && await okxDiv.asElement()) {
          await okxDiv.click();
          console.log('✅ 已点击OKX登录选项');
        } else {
          // 如果找不到OKX，则点击备选元素
          await humanLikeMouseMove(page, 'div.col-span-2:nth-child(4)');
          await page.click('div.col-span-2:nth-child(4)');
          console.log('✅ 已点击备选登录选项');
        }

        // 等待10-15秒以供其他程序进行钱包登录操作
        const walletWaitTime = Math.floor(Math.random() * (15000 - 10000 + 1)) + 10000;
        console.log(`等待 ${walletWaitTime/1000} 秒以供钱包登录...`);
        await new Promise(resolve => setTimeout(resolve, walletWaitTime));
      }
    }

    console.log('✅ 登录流程完成');
  }, 2, '登录流程');
}

/**
 * 执行跳过教程流程
 * @param {Page} page Puppeteer页面对象
 */
async function executeTutorialFlow(page) {
  await withRetry(async () => {
    console.log('开始跳过教程流程...');

    // 第一次点击跳过按钮
    await humanLikeMouseMove(page, 'button.w-\\[88px\\]');
    await page.click('button.w-\\[88px\\]');
    console.log('✅ 已点击第一次跳过按钮');

    // 等待1-2秒
    await randomDelay(1000, 2000);

    // 第二次点击跳过按钮
    await humanLikeMouseMove(page, 'button.w-\\[88px\\]');
    await page.click('button.w-\\[88px\\]');
    console.log('✅ 已点击第二次跳过按钮');

    // 等待1-2秒
    await randomDelay(1000, 2000);

    console.log('✅ 跳过教程流程完成');
  }, 2, '跳过教程流程');
}

/**
 * 执行签到领水流程
 * @param {Page} page Puppeteer页面对象
 * @param {string} copiedAddress 复制的地址
 */
async function executeCheckinFlow(page, copiedAddress) {
  console.log('开始签到领水流程...');

  // 步骤1: 点击 svg.h-auto
  await withRetry(async () => {
    await page.waitForSelector('svg.h-auto', { timeout: 10000 });
    await humanLikeMouseMove(page, 'svg.h-auto');
    await page.click('svg.h-auto');
    console.log('✅ 已点击 svg.h-auto');

    // 等待1-3秒
    await randomDelay(1000, 3000);
  }, 2, '点击svg.h-auto');

  // 步骤2: 点击包含 https://irys.xyz/faucet 链接的元素
  await withRetry(async () => {
    const linkElement = await page.evaluateHandle(() => {
      const elements = Array.from(document.querySelectorAll('.text-size-12 .text-text-linkBase, *'));
      return elements.find(el => el.textContent && el.textContent.includes('https://irys.xyz/faucet')) ||
             elements.find(el => el.href && el.href.includes('https://irys.xyz/faucet'));
    });

    if (linkElement && await linkElement.asElement()) {
      await linkElement.click();
      console.log('✅ 已点击水龙头链接');
    } else {
      // 备选方案：直接点击选择器
      await humanLikeMouseMove(page, '.text-size-12 .text-text-linkBase');
      await page.click('.text-size-12 .text-text-linkBase');
      console.log('✅ 已点击备选链接元素');
    }

    // 等待1-2秒
    await randomDelay(1000, 2000);
  }, 2, '点击水龙头链接');

  // 步骤3: 点击 Continue to Access 按钮
  await withRetry(async () => {
    const continueButton = await page.evaluateHandle(() => {
      const elements = Array.from(document.querySelectorAll('div.w-full.px-7, *'));
      return elements.find(el => el.textContent && el.textContent.includes('Continue to Access'));
    });

    if (continueButton && await continueButton.asElement()) {
      await continueButton.click();
      console.log('✅ 已点击 Continue to Access 按钮');
    } else {
      // 备选方案
      await humanLikeMouseMove(page, 'div.w-full.px-7');
      await page.click('div.w-full.px-7');
      console.log('✅ 已点击备选按钮');
    }

    // 等待13-15秒以供新网页打开并加载完成
    const newPageWaitTime = Math.floor(Math.random() * (15000 - 13000 + 1)) + 13000;
    console.log(`等待 ${newPageWaitTime/1000} 秒以供新页面加载...`);
    await new Promise(resolve => setTimeout(resolve, newPageWaitTime));
  }, 2, '点击Continue按钮');

  // 步骤4-6: 在新页面中执行领水操作
  await withRetry(async () => {
    // 获取所有页面
    const pages = await page.browser().pages();
    let faucetPage = null;

    // 查找水龙头页面
    for (const p of pages) {
      const url = p.url();
      if (url.includes('irys.xyz/faucet')) {
        faucetPage = p;
        break;
      }
    }

    if (!faucetPage) {
      throw new Error('未找到水龙头页面');
    }

    console.log('✅ 找到水龙头页面，开始领水操作');

    // 在水龙头页面中输入地址
    await faucetPage.waitForSelector('input.placeholder\\:text-white\\/60', { timeout: 10000 });
    await humanLikeMouseMove(faucetPage, 'input.placeholder\\:text-white\\/60');
    await faucetPage.click('input.placeholder\\:text-white\\/60');

    // 粘贴地址
    if (copiedAddress) {
      await faucetPage.type('input.placeholder\\:text-white\\/60', copiedAddress);
      console.log('✅ 已粘贴地址');
    } else {
      // 如果没有复制到地址，尝试粘贴剪贴板内容
      await faucetPage.keyboard.down('Control');
      await faucetPage.keyboard.press('v');
      await faucetPage.keyboard.up('Control');
      console.log('✅ 已尝试粘贴剪贴板内容');
    }

    // 等待1-2秒
    await randomDelay(1000, 2000);

    // 第一次点击领水按钮
    await faucetPage.waitForSelector('button.disabled\\:opacity-50', { timeout: 10000 });
    await humanLikeMouseMove(faucetPage, 'button.disabled\\:opacity-50');
    await faucetPage.click('button.disabled\\:opacity-50');
    console.log('✅ 已点击第一次领水按钮');

    // 等待5-7秒
    const firstWaitTime = Math.floor(Math.random() * (7000 - 5000 + 1)) + 5000;
    console.log(`等待 ${firstWaitTime/1000} 秒...`);
    await new Promise(resolve => setTimeout(resolve, firstWaitTime));

    // 第二次点击领水按钮
    await humanLikeMouseMove(faucetPage, 'button.disabled\\:opacity-50');
    await faucetPage.click('button.disabled\\:opacity-50');
    console.log('✅ 已点击第二次领水按钮');

    // 等待5-7秒
    const secondWaitTime = Math.floor(Math.random() * (7000 - 5000 + 1)) + 5000;
    console.log(`等待 ${secondWaitTime/1000} 秒以供领水完成...`);
    await new Promise(resolve => setTimeout(resolve, secondWaitTime));

    // 关闭水龙头页面
    await faucetPage.close();
    console.log('✅ 已关闭水龙头页面，返回任务页面');

  }, 2, '水龙头页面操作');

  // 步骤7: 点击 svg.p-2 元素
  await withRetry(async () => {
    await page.waitForSelector('svg.p-2', { timeout: 10000 });
    await humanLikeMouseMove(page, 'svg.p-2');
    await page.click('svg.p-2');
    console.log('✅ 已点击 svg.p-2');

    // 等待3-5秒
    const waitTime = Math.floor(Math.random() * (5000 - 3000 + 1)) + 3000;
    console.log(`等待 ${waitTime/1000} 秒...`);
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // 检查是否出现钱包连接选项
    const walletDiv = await page.$('div.col-span-2:nth-child(4)');
    if (walletDiv) {
      console.log('🔄 检测到钱包连接选项');

      // 优先查找包含OKX文字的div元素
      const okxDiv = await page.evaluateHandle(() => {
        const elements = Array.from(document.querySelectorAll('div'));
        return elements.find(el => el.textContent && el.textContent.includes('OKX'));
      });

      if (okxDiv && await okxDiv.asElement()) {
        await okxDiv.click();
        console.log('✅ 已点击OKX钱包选项');
      } else {
        await humanLikeMouseMove(page, 'div.col-span-2:nth-child(4)');
        await page.click('div.col-span-2:nth-child(4)');
        console.log('✅ 已点击备选钱包选项');
      }

      // 等待10-15秒以供其他程序进行钱包登录操作
      const walletWaitTime = Math.floor(Math.random() * (15000 - 10000 + 1)) + 10000;
      console.log(`等待 ${walletWaitTime/1000} 秒以供钱包操作...`);
      await new Promise(resolve => setTimeout(resolve, walletWaitTime));
    }

  }, 2, '点击svg.p-2');

  // 步骤8: 点击 Claim 100 Points 按钮
  await withRetry(async () => {
    const claimButton = await page.evaluateHandle(() => {
      const elements = Array.from(document.querySelectorAll('.justify-end:nth-child(1) > .flex:nth-child(2), *'));
      return elements.find(el => el.textContent && el.textContent.includes('Claim 100 Points'));
    });

    if (claimButton && await claimButton.asElement()) {
      await claimButton.click();
      console.log('✅ 已点击 Claim 100 Points 按钮');
    } else {
      // 备选方案
      await humanLikeMouseMove(page, '.justify-end:nth-child(1) > .flex:nth-child(2)');
      await page.click('.justify-end:nth-child(1) > .flex:nth-child(2)');
      console.log('✅ 已点击备选Claim按钮');
    }

    // 等待17-20秒
    const claimWaitTime = Math.floor(Math.random() * (20000 - 17000 + 1)) + 17000;
    console.log(`等待 ${claimWaitTime/1000} 秒...`);
    await new Promise(resolve => setTimeout(resolve, claimWaitTime));
  }, 2, '点击Claim按钮');

  // 步骤9: 点击 Claim Directly 按钮
  await withRetry(async () => {
    const claimDirectlyButton = await page.evaluateHandle(() => {
      const elements = Array.from(document.querySelectorAll('.gap-3 > .border, *'));
      return elements.find(el => el.textContent && el.textContent.includes('Claim Directly'));
    });

    if (claimDirectlyButton && await claimDirectlyButton.asElement()) {
      await claimDirectlyButton.click();
      console.log('✅ 已点击 Claim Directly 按钮');
    } else {
      // 备选方案
      await humanLikeMouseMove(page, '.gap-3 > .border');
      await page.click('.gap-3 > .border');
      console.log('✅ 已点击备选Claim Directly按钮');
    }

    // 等待10-15秒以供其他程序进行钱包确认操作
    const confirmWaitTime = Math.floor(Math.random() * (15000 - 10000 + 1)) + 10000;
    console.log(`等待 ${confirmWaitTime/1000} 秒以供钱包确认...`);
    await new Promise(resolve => setTimeout(resolve, confirmWaitTime));

    console.log('✅ 签到领水流程完成');
  }, 2, '点击Claim Directly按钮');
}

/**
 * 带重试机制的函数包装器
 * @param {Function} fn 要执行的异步函数
 * @param {number} maxRetries 最大重试次数
 * @param {string} stepName 步骤名称，用于日志
 */
async function withRetry(fn, maxRetries, stepName) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      if (attempt > 1) {
        console.log(`🔄 ${stepName} - 第 ${attempt}/${maxRetries} 次尝试`);
        // 重试前添加延迟
        await randomDelay(1000, 2000);
      }

      return await fn();
    } catch (error) {
      lastError = error;
      console.log(`⚠️ ${stepName} - 尝试 ${attempt}/${maxRetries} 失败: ${error.message}`);
    }
  }

  console.log(`❌ ${stepName} - 达到最大重试次数，跳过此步骤`);
  // 不抛出错误，允许脚本继续执行后续步骤
}

/**
 * 随机延迟函数
 * @param {number} min 最小延迟时间(ms)
 * @param {number} max 最大延迟时间(ms)
 */
async function randomDelay(min, max) {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  await new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * 模拟人类鼠标移动，添加轻微抖动和非线性移动
 * @param {Page} page Puppeteer页面对象
 * @param {string} selector 目标元素选择器
 */
async function humanLikeMouseMove(page, selector) {
  try {
    // 获取元素位置
    const element = await page.$(selector);
    if (!element) {
      console.log(`⚠️ 未找到元素: ${selector}`);
      return;
    }

    const box = await element.boundingBox();
    if (!box) {
      console.log(`⚠️ 无法获取元素位置: ${selector}`);
      return;
    }

    // 计算目标点(元素中心)
    const targetX = box.x + box.width / 2;
    const targetY = box.y + box.height / 2;

    // 获取当前鼠标位置(默认在页面左上角)
    let currentX = 0;
    let currentY = 0;

    // 生成5-10个路径点模拟人类移动轨迹
    const steps = 5 + Math.floor(Math.random() * 6);

    for (let i = 0; i < steps; i++) {
      // 非线性插值，开始慢，中间快，结束慢
      const progress = i / (steps - 1);
      const easedProgress = 0.5 - 0.5 * Math.cos(Math.PI * progress);

      // 计算下一个位置
      let nextX = currentX + (targetX - currentX) * easedProgress;
      let nextY = currentY + (targetY - currentY) * easedProgress;

      // 添加随机抖动，抖动幅度随进度变化(中间小，两端大)
      const jitterAmount = 5 * (1 - Math.sin(Math.PI * progress));
      nextX += (Math.random() - 0.5) * jitterAmount;
      nextY += (Math.random() - 0.5) * jitterAmount;

      // 移动到下一个位置
      await page.mouse.move(nextX, nextY);

      // 添加随机延迟，模拟人类移动速度变化
      const delay = 50 + Math.random() * 100;
      await new Promise(resolve => setTimeout(resolve, delay));

      // 更新当前位置
      currentX = nextX;
      currentY = nextY;
    }

    // 最后精确移动到目标中心
    await page.mouse.move(targetX, targetY);

  } catch (error) {
    console.log(`⚠️ 模拟鼠标移动失败: ${error.message}`);
    // 失败时尝试直接移动到元素(回退方案)
    try {
      await page.hover(selector);
    } catch (e) {
      // 忽略hover失败，继续执行
    }
  }
}

module.exports = irysDailyWater;
